import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { BACKEND_URL } from "./utils/api";

// Simple CSS animations instead of framer-motion for better compatibility
const fadeInUp = {
  animation: "fadeInUp 0.6s ease-out forwards",
  opacity: 0,
  transform: "translateY(20px)"
};

// Add CSS keyframes to the component
const animationStyles = `
  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

export default function NewTicketDetailsForm({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [hierarchicalData, setHierarchicalData] = useState(null);

  const [formData, setFormData] = useState({
    sensorType: "",
    familyName: "",
    modelNumber: "",
    serialNumber: "",
    sdkSoftwareUsed: "",
    sdkVersion: "",
    programmingLanguage: "",
    cameraConfigurationTool: "",
    operatingSystemDetailed: "",
    purchasedFrom: "",
    yearOfPurchase: "",
    poNumber: "",
    software: "",
  });

  // Load hierarchical selection data
  useEffect(() => {
    const storedData = sessionStorage.getItem('hierarchicalSelection');
    if (!storedData) {
      // No hierarchical selection found, redirect back
      navigate('/hierarchical-selection');
      return;
    }

    try {
      const data = JSON.parse(storedData);
      setHierarchicalData(data);
      
      // Pre-populate form fields based on hierarchical selection
      setFormData(prev => ({
        ...prev,
        // Set sensor type if it's a camera
        sensorType: data.productSubcategory === "Area Scan" || data.productSubcategory === "Line Scan" 
          ? data.productSubcategory : "",
        // Set family name from hierarchy
        familyName: data.productFamily || "",
        // Set SDK if it's software
        sdkSoftwareUsed: data.productCategory === "Software" ? data.productSubcategory : "",
      }));
    } catch (err) {
      console.error("Error parsing hierarchical data:", err);
      navigate('/hierarchical-selection');
    }
  }, [navigate]);

  // Auto-scroll to bottom when component mounts and when form is rendered
  useEffect(() => {
    const scrollToBottom = () => {
      window.scrollTo({
        top: document.documentElement.scrollHeight,
        behavior: 'smooth'
      });
    };

    // Scroll after a short delay to ensure content is rendered
    const timer = setTimeout(scrollToBottom, 500);
    return () => clearTimeout(timer);
  }, [hierarchicalData]); // Trigger when hierarchical data is loaded

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError("");
  };

  const validateForm = () => {
    const requiredFields = [
      'modelNumber', 'serialNumber', 'purchasedFrom', 'yearOfPurchase', 'operatingSystemDetailed', 'poNumber'
    ];

    for (let field of requiredFields) {
      if (!formData[field] || formData[field].trim() === "") {
        return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;
      }
    }

    return null;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    if (!hierarchicalData) {
      setError("Hierarchical selection data is missing. Please start over.");
      return;
    }

    setLoading(true);
    setError("");

    // Scroll to bottom to show loading state and any potential errors
    window.scrollTo({
      top: document.documentElement.scrollHeight,
      behavior: 'smooth'
    });

    try {
      // Get brand from sessionStorage
      const selectedBrand = sessionStorage.getItem('selectedBrand') || '';

      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          // Hierarchical data
          product_type: hierarchicalData.productCategory,
          product_hierarchy_path: hierarchicalData.path,
          product_category: hierarchicalData.productCategory,
          product_subcategory: hierarchicalData.productSubcategory,
          product_family: hierarchicalData.productFamily,
          product_interface: hierarchicalData.productInterface,

          // Form data
          brand: selectedBrand,
          sensor_type: formData.sensorType,
          family_name: formData.familyName,
          model_number: formData.modelNumber,
          serial_number: formData.serialNumber,
          sdk_software_used: formData.sdkSoftwareUsed,
          sdk_version: formData.sdkVersion,
          programming_language: formData.programmingLanguage,
          camera_configuration_tool: formData.cameraConfigurationTool,
          operating_system_detailed: formData.operatingSystemDetailed,
          purchased_from: formData.purchasedFrom,
          year_of_purchase: parseInt(formData.yearOfPurchase) || new Date().getFullYear(),
          po_number: formData.poNumber,
          software: formData.software,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Clear the hierarchical selection and selected brand from session storage
        sessionStorage.removeItem('hierarchicalSelection');
        sessionStorage.removeItem('selectedBrand');
        // Redirect to problem category selection
        navigate(`/problem-categories?ticket=${data.ticket_number}`);
      } else {
        setError(data.message || "Failed to create ticket. Please try again.");
      }
    } catch (err) {
      console.error("Error creating ticket:", err);
      setError("Network error. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    sessionStorage.removeItem('hierarchicalSelection');
    sessionStorage.removeItem('selectedBrand');
    navigate("/select-brand");
  };

  if (!hierarchicalData) {
    return (
      <div style={{ 
        padding: "40px", 
        textAlign: "center",
        fontFamily: "Arial, sans-serif"
      }}>
        <h2>Loading...</h2>
      </div>
    );
  }

  return (
    <>
      <style>{animationStyles}</style>
      <div style={{
        minHeight: "100vh",
        background: "linear-gradient(to bottom right, #1E3A8A, #3B82F6)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        padding: "2rem",
        fontFamily: "Arial, sans-serif"
      }}>
        <div className="glass-card" style={{
          maxWidth: "800px",
          width: "100%",
          maxHeight: "90vh",
          overflowY: "auto",
          ...fadeInUp
        }}>
        <h1 className="blue-heading" style={{
          color: "white",
          marginBottom: "1.5rem",
          textAlign: "center",
          fontSize: "3rem",
          fontWeight: "bold"
        }}>
          Product Details
        </h1>

        {/* Show selected hierarchy */}
        <div style={{
          backgroundColor: "#DBEAFE",
          padding: "16px 20px",
          borderRadius: "0.75rem",
          marginBottom: "2rem",
          border: "2px solid #BFDBFE"
        }}>
          <h3 style={{ margin: "0 0 10px 0", color: "#1E40AF", fontWeight: "600" }}>Selected Product:</h3>
          <p style={{ margin: "0", fontSize: "1rem", fontWeight: "600", color: "#1E3A8A" }}>
            {hierarchicalData.path.join(' > ')}
          </p>
        </div>

        <p style={{
          fontSize: "1.2rem",
          color: "rgba(255, 255, 255, 0.9)",
          marginBottom: "2rem",
          textAlign: "center",
          lineHeight: "1.6"
        }}>
          Please provide additional details about your product:
        </p>

        {error && (
          <div style={{
            backgroundColor: "#FEE2E2",
            color: "#DC2626",
            padding: "12px 16px",
            borderRadius: "0.75rem",
            marginBottom: "20px",
            border: "2px solid #FECACA",
            fontWeight: "600"
          }}>
            {error}
          </div>
        )}

        <form
          onSubmit={handleSubmit}
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "20px"
          }}
        >


          {/* Model Number */}
          <div>
            <label style={{ display: "block", marginBottom: "8px", fontWeight: "600", color: "rgba(255, 255, 255, 0.9)", fontSize: "1rem" }}>
              Model Number *
            </label>
            <input
              type="text"
              name="modelNumber"
              value={formData.modelNumber}
              onChange={handleInputChange}
              required
              className="glass-input"
              style={{
                width: "100%",
                boxSizing: "border-box"
              }}
              placeholder="Enter exact model number"
            />
          </div>

        {/* Serial Number */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "600", color: "rgba(255, 255, 255, 0.9)", fontSize: "1rem" }}>
            Serial Number *
          </label>
          <input
            type="text"
            name="serialNumber"
            value={formData.serialNumber}
            onChange={handleInputChange}
            required
            className="glass-input"
            style={{
              width: "100%",
              boxSizing: "border-box"
            }}
            placeholder="Enter serial number"
          />
        </div>

        {/* SDK Version - only show for software */}
        {hierarchicalData.productCategory === "Software" && (
          <div>
            <label style={{ display: "block", marginBottom: "8px", fontWeight: "600", color: "rgba(255, 255, 255, 0.9)", fontSize: "1rem" }}>
              SDK Version
            </label>
            <input
              type="text"
              name="sdkVersion"
              value={formData.sdkVersion}
              onChange={handleInputChange}
              className="glass-input"
              style={{
                width: "100%",
                boxSizing: "border-box"
              }}
              placeholder="e.g., 8.90, 3.1"
            />
          </div>
        )}

        {/* Software */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "600", color: "rgba(255, 255, 255, 0.9)", fontSize: "1rem" }}>
            Software
          </label>
          <select
            name="software"
            value={formData.software}
            onChange={handleInputChange}
            className="glass-input"
            style={{
              width: "100%",
              boxSizing: "border-box"
            }}
          >
            <option value="">Select Software</option>
            <option value="Sapera LT (v9.0)">Sapera LT (v9.0)</option>
            <option value="Spinnaker (v4.2)">Spinnaker (v4.2)</option>
          </select>
        </div>

        {/* Programming Language */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "600", color: "rgba(255, 255, 255, 0.9)", fontSize: "1rem" }}>
            Programming Language
          </label>
          <select
            name="programmingLanguage"
            value={formData.programmingLanguage}
            onChange={handleInputChange}
            className="glass-input"
            style={{
              width: "100%",
              boxSizing: "border-box"
            }}
          >
            <option value="">Select Language</option>
            <option value="C++">C++</option>
            <option value="C#">C#</option>
            <option value="Python">Python</option>
            <option value="Java">Java</option>
            <option value="LabVIEW">LabVIEW</option>
            <option value="Other">Other</option>
          </select>
        </div>

        {/* Operating System */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "600", color: "rgba(255, 255, 255, 0.9)", fontSize: "1rem" }}>
            Operating System *
          </label>
          <select
            name="operatingSystemDetailed"
            value={formData.operatingSystemDetailed}
            onChange={handleInputChange}
            required
            className="glass-input"
            style={{
              width: "100%",
              boxSizing: "border-box"
            }}
          >
            <option value="">Select Operating System</option>
            <option value="Windows 10">Windows 10</option>
            <option value="Windows 11">Windows 11</option>
            <option value="Ubuntu 18.04">Ubuntu 18.04</option>
            <option value="Ubuntu 20.04">Ubuntu 20.04</option>
            <option value="Ubuntu 22.04">Ubuntu 22.04</option>
            <option value="CentOS 7">CentOS 7</option>
            <option value="CentOS 8">CentOS 8</option>
            <option value="Other Linux">Other Linux</option>
            <option value="macOS">macOS</option>
            <option value="Other">Other</option>
          </select>
        </div>

        {/* Purchased From */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "600", color: "rgba(255, 255, 255, 0.9)", fontSize: "1rem" }}>
            Purchased From *
          </label>
          <input
            type="text"
            name="purchasedFrom"
            value={formData.purchasedFrom}
            onChange={handleInputChange}
            required
            className="glass-input"
            style={{
              width: "100%",
              boxSizing: "border-box"
            }}
            placeholder="Dealer/Distributor name"
          />
        </div>

        {/* Year of Purchase */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "600", color: "rgba(255, 255, 255, 0.9)", fontSize: "1rem" }}>
            Year of Purchase *
          </label>
          <select
            name="yearOfPurchase"
            value={formData.yearOfPurchase}
            onChange={handleInputChange}
            required
            className="glass-input"
            style={{
              width: "100%",
              boxSizing: "border-box"
            }}
          >
            <option value="">Select Year</option>
            {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i).map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
        </div>

        {/* PO Number */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "600", color: "rgba(255, 255, 255, 0.9)", fontSize: "1rem" }}>
            PO Number *
          </label>
          <input
            type="text"
            name="poNumber"
            value={formData.poNumber}
            onChange={handleInputChange}
            required
            className="glass-input"
            style={{
              width: "100%",
              boxSizing: "border-box"
            }}
            placeholder="Purchase Order Number"
          />
        </div>

          {/* Submit Buttons */}
          <div style={{
            display: "flex",
            gap: "16px",
            justifyContent: "center",
            marginTop: "2rem"
          }}>
            <button
              type="button"
              onClick={handleCancel}
              style={{
                padding: "14px 24px",
                backgroundColor: "#6B7280",
                color: "white",
                border: "none",
                borderRadius: "0.75rem",
                fontSize: "1rem",
                fontWeight: "600",
                cursor: "pointer",
                transition: "all 0.2s ease-in-out",
                boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
              }}
              onMouseOver={(e) => {
                e.target.style.backgroundColor = "#4B5563";
                e.target.style.transform = "translateY(-1px)";
                e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
              }}
              onMouseOut={(e) => {
                e.target.style.backgroundColor = "#6B7280";
                e.target.style.transform = "translateY(0)";
                e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
              }}
            >
              Back
            </button>

            <button
              type="submit"
              disabled={loading}
              style={{
                padding: "14px 24px",
                background: loading ? "#9CA3AF" : "linear-gradient(to right, #3B82F6, #1E40AF)",
                color: "white",
                border: "none",
                borderRadius: "0.75rem",
                fontSize: "1rem",
                fontWeight: "bold",
                cursor: loading ? "not-allowed" : "pointer",
                transition: "all 0.2s ease-in-out",
                boxShadow: loading ? "none" : "0 4px 6px rgba(0, 0, 0, 0.1)"
              }}
              onMouseOver={(e) => {
                if (!loading) {
                  e.target.style.background = "linear-gradient(to right, #1E40AF, #1E3A8A)";
                  e.target.style.transform = "translateY(-1px)";
                  e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
                }
              }}
              onMouseOut={(e) => {
                if (!loading) {
                  e.target.style.background = "linear-gradient(to right, #3B82F6, #1E40AF)";
                  e.target.style.transform = "translateY(0)";
                  e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
                }
              }}
            >
              {loading ? "Creating Ticket..." : "Continue"}
            </button>
          </div>
        </form>
        </div>
      </div>
    </>
  );
}
