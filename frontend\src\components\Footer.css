.global-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to bottom right, #1E3A8A, #3B82F6);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: 8px 0;
  text-align: center;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.global-footer p {
  margin: 0;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* Hide footer completely on chatbot pages */
.chat-container .global-footer {
  display: none !important;
}

/* Add bottom padding to body when footer is present */
body.has-footer {
  padding-bottom: 40px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .global-footer {
    padding: 6px 0;
  }
  
  .global-footer p {
    font-size: 11px;
  }
  
  body.has-footer {
    padding-bottom: 35px;
  }
}
