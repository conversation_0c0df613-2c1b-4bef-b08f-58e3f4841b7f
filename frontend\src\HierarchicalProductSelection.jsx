import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { BACKEND_URL } from "./utils/api";

export default function HierarchicalProductSelection({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  
  const [hierarchy, setHierarchy] = useState({});
  const [selectedPath, setSelectedPath] = useState([]);
  const [currentLevel, setCurrentLevel] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Load product hierarchy from backend
  useEffect(() => {
    const fetchHierarchy = async () => {
      try {
        const response = await fetch(`${BACKEND_URL}/api/product_hierarchy/`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });

        const data = await response.json();
        if (response.ok) {
          setHierarchy(data.hierarchy);
          setCurrentLevel(data.hierarchy);
        } else {
          setError(data.message || "Failed to load product hierarchy");
        }
      } catch (err) {
        console.error("Error fetching hierarchy:", err);
        setError("Network error. Please check your connection.");
      } finally {
        setLoading(false);
      }
    };

    fetchHierarchy();
  }, [accessToken]);

  const handleSelection = (key) => {
    const newPath = [...selectedPath, key];
    setSelectedPath(newPath);

    // Navigate deeper into the hierarchy
    const nextLevel = currentLevel[key];
    
    if (nextLevel && typeof nextLevel === 'object' && !Array.isArray(nextLevel)) {
      // Check if this is a leaf node (contains models, versions, etc.)
      const hasSubCategories = Object.keys(nextLevel).some(k => 
        typeof nextLevel[k] === 'object' && 
        !Array.isArray(nextLevel[k]) &&
        !['models', 'versions', 'interfaces', 'platforms', 'types', 'lengths', 'focal_lengths', 'colors'].includes(k)
      );

      if (hasSubCategories) {
        setCurrentLevel(nextLevel);
      } else {
        // This is a leaf node, proceed to details form
        proceedToDetailsForm(newPath, nextLevel);
      }
    } else {
      // This shouldn't happen with our hierarchy structure
      proceedToDetailsForm(newPath, {});
    }
  };

  const handleBack = () => {
    if (selectedPath.length === 0) {
      navigate("/actions");
      return;
    }

    const newPath = selectedPath.slice(0, -1);
    setSelectedPath(newPath);

    // Navigate back up the hierarchy
    let level = hierarchy;
    for (const pathItem of newPath) {
      level = level[pathItem];
    }
    setCurrentLevel(level);
  };

  const proceedToDetailsForm = (path, leafData) => {
    // Store the hierarchical selection in sessionStorage for the details form
    const hierarchicalData = {
      path: path,
      leafData: leafData,
      productCategory: path[0] || '',
      productSubcategory: path[1] || '',
      productFamily: path[2] || '',
      productInterface: path[3] || ''
    };
    
    sessionStorage.setItem('hierarchicalSelection', JSON.stringify(hierarchicalData));
    navigate('/new-ticket-details');
  };

  const getBreadcrumb = () => {
    return selectedPath.join(' > ');
  };

  const getCurrentOptions = () => {
    if (!currentLevel || typeof currentLevel !== 'object') return [];
    
    return Object.keys(currentLevel).filter(key => 
      typeof currentLevel[key] === 'object' && 
      !Array.isArray(currentLevel[key]) &&
      !['models', 'versions', 'interfaces', 'platforms', 'types', 'lengths', 'focal_lengths', 'colors'].includes(key)
    );
  };

  if (loading) {
    return (
      <div style={{
        minHeight: "100vh",
        background: "linear-gradient(to bottom right, #1E3A8A, #3B82F6)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        padding: "2rem",
        fontFamily: "Arial, sans-serif"
      }}>
        <div style={{
          background: "rgba(255, 255, 255, 0.95)",
          borderRadius: "1.5rem",
          padding: "3rem",
          boxShadow: "0 20px 25px rgba(0, 0, 0, 0.25)",
          backdropFilter: "blur(10px)",
          textAlign: "center"
        }}>
          <h2 style={{ color: "#1E3A8A", fontSize: "1.5rem", fontWeight: "600" }}>Loading Product Categories...</h2>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        minHeight: "100vh",
        background: "linear-gradient(to bottom right, #1E3A8A, #3B82F6)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        padding: "2rem",
        fontFamily: "Arial, sans-serif"
      }}>
        <div style={{
          background: "rgba(255, 255, 255, 0.95)",
          borderRadius: "1.5rem",
          padding: "3rem",
          maxWidth: "600px",
          width: "100%",
          boxShadow: "0 20px 25px rgba(0, 0, 0, 0.25)",
          backdropFilter: "blur(10px)"
        }}>
          <div style={{
            backgroundColor: "#FEE2E2",
            color: "#DC2626",
            padding: "20px",
            borderRadius: "0.75rem",
            border: "2px solid #FECACA",
            textAlign: "center",
            marginBottom: "1.5rem"
          }}>
            <h3 style={{ color: "#DC2626", fontWeight: "600" }}>Error Loading Product Categories</h3>
            <p>{error}</p>
            <button
              onClick={() => navigate("/actions")}
              style={{
                padding: "12px 24px",
                backgroundColor: "#2563EB",
                color: "white",
                border: "none",
                borderRadius: "0.75rem",
                cursor: "pointer",
                marginTop: "1rem",
                fontWeight: "600",
                transition: "all 0.2s ease-in-out",
                boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
              }}
              onMouseOver={(e) => {
                e.target.style.backgroundColor = "#1E40AF";
                e.target.style.transform = "translateY(-1px)";
                e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
              }}
              onMouseOut={(e) => {
                e.target.style.backgroundColor = "#2563EB";
                e.target.style.transform = "translateY(0)";
                e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
              }}
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  const currentOptions = getCurrentOptions();

  return (
    <div style={{
      minHeight: "100vh",
      background: "linear-gradient(to bottom right, #1E3A8A, #3B82F6)",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      padding: "2rem",
      fontFamily: "Arial, sans-serif"
    }}>
      <div style={{
        background: "rgba(255, 255, 255, 0.95)",
        borderRadius: "1.5rem",
        padding: "3rem",
        maxWidth: "800px",
        width: "100%",
        boxShadow: "0 20px 25px rgba(0, 0, 0, 0.25)",
        backdropFilter: "blur(10px)",
        maxHeight: "90vh",
        overflowY: "auto"
      }}>
        <h1 style={{
          color: "#1E3A8A",
          marginBottom: "1.5rem",
          textAlign: "center",
          fontSize: "2rem",
          fontWeight: "600"
        }}>
          Select Product Category
        </h1>

        {/* Breadcrumb */}
        {selectedPath.length > 0 && (
          <div style={{
            backgroundColor: "#DBEAFE",
            padding: "12px 16px",
            borderRadius: "0.75rem",
            marginBottom: "1.5rem",
            fontSize: "0.875rem",
            color: "#1E40AF",
            border: "2px solid #BFDBFE"
          }}>
            <strong>Selected:</strong> {getBreadcrumb()}
          </div>
        )}

        <p style={{
          fontSize: "1.2rem",
          color: "#4B5563",
          marginBottom: "2rem",
          textAlign: "center",
          lineHeight: "1.6"
        }}>
          {selectedPath.length === 0
            ? "Please select your product type to begin:"
            : `Please select the ${selectedPath.length === 1 ? 'subcategory' : selectedPath.length === 2 ? 'product family' : 'interface type'}:`
          }
        </p>

        {/* Product Options */}
        <div style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
          gap: "20px",
          marginBottom: "2rem"
        }}>
          {currentOptions.map((option) => (
            <button
              key={option}
              onClick={() => handleSelection(option)}
              style={{
                padding: "16px 20px",
                border: "2px solid #E5E7EB",
                borderRadius: "0.75rem",
                backgroundColor: "#ffffff",
                cursor: "pointer",
                fontSize: "1rem",
                fontWeight: "600",
                color: "#1E3A8A",
                transition: "all 0.2s ease-in-out",
                textAlign: "center",
                minHeight: "80px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
              }}
              onMouseOver={(e) => {
                e.target.style.borderColor = "#3B82F6";
                e.target.style.backgroundColor = "#EFF6FF";
                e.target.style.transform = "translateY(-1px)";
                e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
              }}
              onMouseOut={(e) => {
                e.target.style.borderColor = "#E5E7EB";
                e.target.style.backgroundColor = "#ffffff";
                e.target.style.transform = "translateY(0)";
                e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
              }}
          >
            {option}
          </button>
        ))}
        </div>

        {/* Navigation Buttons */}
        <div style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center"
        }}>
          <button
            onClick={handleBack}
            style={{
              padding: "12px 24px",
              backgroundColor: "#6B7280",
              color: "white",
              border: "none",
              borderRadius: "0.75rem",
              fontSize: "1rem",
              fontWeight: "600",
              cursor: "pointer",
              transition: "all 0.2s ease-in-out",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = "#4B5563";
              e.target.style.transform = "translateY(-1px)";
              e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = "#6B7280";
              e.target.style.transform = "translateY(0)";
              e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
            }}
          >
            ← {selectedPath.length === 0 ? "Back to Actions" : "Back"}
          </button>

          <div style={{
            fontSize: "0.875rem",
            color: "#4B5563",
            textAlign: "center",
            fontWeight: "600"
          }}>
            Step {selectedPath.length + 1} of 4
          </div>
        </div>
      </div>
    </div>
  );
}
