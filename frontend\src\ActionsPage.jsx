import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function ActionsPage({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const [userName, setUserName] = useState("");
  const [showClosedTickets, setShowClosedTickets] = useState(false);
  const [closedTickets, setClosedTickets] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!accessToken) {
      navigate("/auth");
      return;
    }

    // Fetch user info
    fetch(`${BACKEND_URL}/api/user_info/`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    })
      .then(async (res) => {
        if (!res.ok) {
          throw new Error("User info fetch failed");
        }
        return res.json();
      })
      .then((data) => {
        setUserName(data.name || data.username || data.email);
      })
      .catch((err) => {
        console.error("Failed to fetch user info:", err);
        localStorage.removeItem("access");
        navigate("/auth");
      });
  }, [accessToken, navigate]);

  const handleRaiseNewTicket = () => {
    navigate("/select-brand");
  };

  const handleUsePendingTicket = () => {
    // Navigate to pending tickets list page
    navigate("/pending-tickets");
  };

  const handleViewClosedTickets = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${BACKEND_URL}/api/closed_tickets/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      const data = await response.json();

      if (response.ok) {
        setClosedTickets(data.tickets || []);
        setShowClosedTickets(true);
      } else {
        console.error("Failed to fetch closed tickets:", data);
        alert("Failed to fetch closed tickets. Please try again.");
      }
    } catch (err) {
      console.error("Error fetching closed tickets:", err);
      alert("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: "100vh",
      background: "linear-gradient(to bottom right, #1E3A8A, #3B82F6)",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      padding: "2rem",
      fontFamily: "Arial, sans-serif"
    }}>
      <div style={{
        background: "rgba(255, 255, 255, 0.95)",
        borderRadius: "1.5rem",
        padding: "3rem",
        maxWidth: "600px",
        width: "100%",
        boxShadow: "0 20px 25px rgba(0, 0, 0, 0.25)",
        backdropFilter: "blur(10px)",
        textAlign: "center"
      }}>
        <h1 style={{
          color: "#1E3A8A",
          marginBottom: "1.5rem",
          fontSize: "2.5rem",
          fontWeight: "600"
        }}>
          Welcome, {userName}!
        </h1>

        <p style={{
          fontSize: "1.2rem",
          color: "#4B5563",
          marginBottom: "2.5rem",
          lineHeight: "1.6"
        }}>
          How can we help you today? Please choose one of the options below:
        </p>

        <div style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          alignItems: "center"
        }}>

          {/* Raise New Ticket Button */}
          <button
            onClick={handleRaiseNewTicket}
            style={{
              backgroundColor: "#2563EB",
              color: "white",
              border: "none",
              padding: "16px 32px",
              fontSize: "1.2rem",
              borderRadius: "0.75rem",
              cursor: "pointer",
              width: "280px",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
              transition: "all 0.2s ease-in-out",
              fontWeight: "600",
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = "#1E40AF";
              e.target.style.transform = "translateY(-1px)";
              e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = "#2563EB";
              e.target.style.transform = "translateY(0)";
              e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
            }}
          >
            🎫 Raise New Ticket
          </button>

          {/* Use Pending Ticket Button */}
          <button
            onClick={handleUsePendingTicket}
            style={{
              backgroundColor: "#3B82F6",
              color: "white",
              border: "none",
              padding: "16px 32px",
              fontSize: "1.2rem",
              borderRadius: "0.75rem",
              cursor: "pointer",
              width: "280px",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
              transition: "all 0.2s ease-in-out",
              fontWeight: "600",
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = "#2563EB";
              e.target.style.transform = "translateY(-1px)";
              e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = "#3B82F6";
              e.target.style.transform = "translateY(0)";
              e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
            }}
          >
            📋 Use Pending Ticket
          </button>

          {/* Closed Tickets Button */}
          <button
            onClick={handleViewClosedTickets}
            disabled={loading}
            style={{
              backgroundColor: loading ? "#9CA3AF" : "#1E3A8A",
              color: "white",
              border: "none",
              padding: "16px 32px",
              fontSize: "1.2rem",
              borderRadius: "0.75rem",
              cursor: loading ? "not-allowed" : "pointer",
              width: "280px",
              boxShadow: loading ? "none" : "0 4px 6px rgba(0, 0, 0, 0.1)",
              transition: "all 0.2s ease-in-out",
              fontWeight: "600",
            }}
            onMouseOver={(e) => {
              if (!loading) {
                e.target.style.backgroundColor = "#1E40AF";
                e.target.style.transform = "translateY(-1px)";
                e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
              }
            }}
            onMouseOut={(e) => {
              if (!loading) {
                e.target.style.backgroundColor = "#1E3A8A";
                e.target.style.transform = "translateY(0)";
                e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
              }
            }}
          >
            {loading ? "Loading..." : "📁 Closed Tickets"}
          </button>
        </div>

        {/* Closed Tickets Modal */}
        {showClosedTickets && (
          <div style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(30, 58, 138, 0.8)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 1000,
            backdropFilter: "blur(4px)"
          }}>
            <div style={{
              backgroundColor: "white",
              borderRadius: "1rem",
              padding: "2rem",
              maxWidth: "800px",
              maxHeight: "80vh",
              overflowY: "auto",
              width: "90%",
              boxShadow: "0 20px 25px rgba(0, 0, 0, 0.25)"
            }}>
              <div style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "20px"
              }}>
                <h2 style={{ margin: 0, color: "#1E3A8A", fontSize: "1.5rem", fontWeight: "600" }}>Closed Tickets</h2>
                <button
                  onClick={() => setShowClosedTickets(false)}
                  style={{
                    backgroundColor: "#EF4444",
                    color: "white",
                    border: "none",
                    borderRadius: "0.5rem",
                    padding: "8px 16px",
                    cursor: "pointer",
                    fontWeight: "600",
                    transition: "all 0.2s ease-in-out"
                  }}
                  onMouseOver={(e) => {
                    e.target.style.backgroundColor = "#DC2626";
                  }}
                  onMouseOut={(e) => {
                    e.target.style.backgroundColor = "#EF4444";
                  }}
                >
                  ✕ Close
                </button>
              </div>

            {closedTickets.length === 0 ? (
              <p style={{ textAlign: "center", color: "#666", fontSize: "1.1rem" }}>
                No closed tickets found.
              </p>
            ) : (
              <div style={{ display: "flex", flexDirection: "column", gap: "15px" }}>
                {closedTickets.map((ticket) => (
                  <div
                    key={ticket.ticket_number}
                    style={{
                      border: "1px solid #ddd",
                      borderRadius: "6px",
                      padding: "15px",
                      backgroundColor: "#f9f9f9"
                    }}
                  >
                    <div style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "flex-start",
                      marginBottom: "10px"
                    }}>
                      <h3 style={{ margin: 0, color: "#333", fontSize: "1.1rem" }}>
                        {ticket.ticket_number}
                      </h3>
                      <span style={{
                        backgroundColor: "#4CAF50",
                        color: "white",
                        padding: "4px 8px",
                        borderRadius: "4px",
                        fontSize: "0.8rem",
                        fontWeight: "bold"
                      }}>
                        CLOSED
                      </span>
                    </div>
                    <p style={{ margin: "5px 0", color: "#666" }}>
                      <strong>Product:</strong> {ticket.product_type} - {ticket.model}
                    </p>
                    <p style={{ margin: "5px 0", color: "#666" }}>
                      <strong>Issue:</strong> {ticket.issue}
                    </p>
                    <p style={{ margin: "5px 0", color: "#666" }}>
                      <strong>Solution:</strong> {ticket.solution_summary || "No solution summary available"}
                    </p>
                    <p style={{ margin: "5px 0", color: "#999", fontSize: "0.9rem" }}>
                      <strong>Closed:</strong> {new Date(ticket.last_activity).toLocaleDateString()}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      </div>
    </div>
  );
}
