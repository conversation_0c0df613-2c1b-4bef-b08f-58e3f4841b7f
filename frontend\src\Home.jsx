
import React, { useState, useEffect, useRef } from "react";
import { useParams } from "react-router-dom";
import { YesNoButtons, FileDownloadButtons, TicketCloseButtons } from "./YesNoButtons";
import { apiGet, apiPost, BACKEND_URL } from "./utils/api";
import "./App.css";

export default function Home({ token }) {
  const { ticketId } = useParams(); // Get ticket ID from URL if present
  const accessToken = token || localStorage.getItem("access");

  // --- States ---
  const [query, setQuery] = useState("");
  const [messages, setMessages] = useState([]);
  const [promptTemplate, setPromptTemplate] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [mode, setMode] = useState("strict");
  const [pendingFiles, setPendingFiles] = useState(null);
  const [orgVerified, setOrgVerified] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [username, setUserName] = useState("");
  const [askRaiseTicket, setAskRaiseTicket] = useState(false);
  const [awaitingCloseConfirmation, setAwaitingCloseConfirmation] = useState(false);
  const [awaitingOtherQueries, setAwaitingOtherQueries] = useState(false);
  const [pendingTickets, setPendingTickets] = useState([]);
  const [awaitingPendingChoice, setAwaitingPendingChoice] = useState(false);
  const [awaitingTicketSelect, setAwaitingTicketSelect] = useState(false);
  const [activeTicket, setActiveTicket] = useState(null);
  const [ticketRefused, setTicketRefused] = useState(false);
  const [queriesAfterNoTicket, setQueriesAfterNoTicket] = useState(0);
  const [awaitingUnrelatedQueryResponse, setAwaitingUnrelatedQueryResponse] = useState(false);
  const [awaitingCloseOrKeepOpen, setAwaitingCloseOrKeepOpen] = useState(false);
  const [sessionKeepAlive, setSessionKeepAlive] = useState(null);
  const MAX_QUERIES_AFTER_NO_TICKET = 10;
  const [tickets, setTickets] = useState([]);
  const [showTickets, setShowTickets] = useState(false);
  const [ticketQueryCount, setTicketQueryCount] = useState(0);


  // --- New ticket states ---
  const [ticketStep, setTicketStep] = useState(0);
  const [ticketData, setTicketData] = useState({
    productType: "",
    purchasedFrom: "",
    yearOfPurchase: "",
    productName: "",
    model: "",
    serialNo: "",
    operatingSystem: "",
  });
  const [awaitingProblemDescription, setAwaitingProblemDescription] = useState(false);
  const [currentTicketNumber, setCurrentTicketNumber] = useState(null);

  const ticketQuestions = [
    "Please select the product type using the dropdown below:",
    "Please enter the 'Purchased From' information:",
    "Please enter the 'Year of Purchase':",
    "Please enter the 'Product Name':",
    "Please enter the 'Model':",
    "Please enter the 'Serial Number':",
    "Please enter the 'Operating System':",
  ];

  const productTypeOptions = ["Camera", "Frame Grabber", "Accessories", "Software"];

  // Auto-scroll messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, loading, error]);

  const messagesEndRef = useRef(null);

  // Fetch prompt template
  useEffect(() => {
    fetch(`${BACKEND_URL}/api/prompts/?type=chat`)
      .then((res) => res.json())
      .then((data) => setPromptTemplate(data.template))
      .catch((err) => {
        console.error("Failed to fetch prompt template:", err);
        setPromptTemplate(null);
      });
  }, []);

  // Check for direct ticket access via URL params or query params
  useEffect(() => {
    if (ticketId) {
      // Direct ticket access via /chatbot/:ticketId
      loadDirectTicketSession(ticketId);
    } else {
      // Check for legacy URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const isPendingMode = urlParams.get('mode') === 'pending';
      const isNewTicketMode = urlParams.get('mode') === 'new';
      const ticketNumber = urlParams.get('ticket');

      if (isPendingMode && ticketNumber) {
        // Load specific pending ticket
        loadSpecificPendingTicket(ticketNumber);
      } else if (isPendingMode) {
        // Load latest pending ticket (fallback)
        loadPendingTicketDirectly();
      } else if (isNewTicketMode && ticketNumber) {
        // Load the newly created ticket
        loadNewTicket(ticketNumber);
      }
    }
  }, [ticketId]);

  // Fetch username and welcome message
  useEffect(() => {
    if (!accessToken) return;

    const urlParams = new URLSearchParams(window.location.search);
    const isPendingMode = urlParams.get('mode') === 'pending';

    if (isPendingMode) {
      return; // Skip normal flow for pending mode
    }

    apiGet(`${BACKEND_URL}/api/user_info/`)
      .then(async (res) => {
        if (!res.ok) {
          const errorText = await res.text();
          console.error("User info fetch failed:", res.status, errorText);
          throw new Error("User info fetch failed");
        }
        return res.json();
      })
      .then((data) => {
        const name = data.name || data.username || data.email;
        if (!name) throw new Error("Name missing in response");

        setUserName(name);
        setMessages([
          {
            id: 1,
            type: "bot",
            content: `👋 Welcome, ${name}! Please enter your organization name to verify your account.`,
            timestamp: new Date(),
          },
        ]);
      })
      .catch((err) => {
        console.error("Failed to fetch user info:", err.message);
        localStorage.removeItem("access");
        window.location.href = "/auth";
      });
  }, [accessToken]);

  // Load direct ticket session (for /chatbot/:ticketId route)
  const loadDirectTicketSession = async (ticketNumber) => {
    if (!accessToken) return;

    try {
      // Use the start_ticket_session endpoint
      const response = await apiGet(`${BACKEND_URL}/api/start_ticket_session/${ticketNumber}/`);

      if (response.ok) {
        const sessionData = await response.json();

        // Set user info and ticket state
        setUserName(sessionData.ticket?.user?.name || sessionData.ticket?.user?.official_email?.split('@')[0] || 'User');
        setOrgVerified(true);
        setActiveTicket(ticketNumber);
        setCurrentTicketNumber(ticketNumber);

        // Add the initial welcome message
        setMessages([
          {
            id: 1,
            type: "bot",
            content: sessionData.initial_message,
            timestamp: new Date(),
          }
        ]);

        // Set awaiting description state if needed
        if (sessionData.awaiting_description) {
          setAwaitingProblemDescription(true);
        }

      } else {
        const errorData = await response.json();
        setError(errorData.error || "Failed to load ticket session.");
      }
    } catch (err) {
      console.error("Failed to load direct ticket session:", err);
      setError("Failed to load ticket session. Please try again.");
    }
  };

  // Load new ticket (for new ticket mode)
  const loadNewTicket = async (ticketNumber) => {
    if (!accessToken) return;

    try {
      // Get user info
      const userResponse = await apiGet(`${BACKEND_URL}/api/user_info/`);

      if (!userResponse.ok) {
        throw new Error("Failed to fetch user info");
      }

      const userData = await userResponse.json();
      const name = userData.name || userData.username || userData.email;
      setUserName(name);
      setOrgVerified(true);
      setActiveTicket(ticketNumber);

      // Fetch the ticket details to show generated content
      try {
        const ticketResponse = await apiGet(`${BACKEND_URL}/api/ticket/${ticketNumber}/`);

        if (ticketResponse.ok) {
          const ticketData = await ticketResponse.json();

          setMessages([
            {
              id: 1,
              type: "bot",
              content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,
              timestamp: new Date(),
            },
            {
              id: 2,
              type: "bot",
              content: `🎫 **Ticket Created:** ${ticketNumber}\n\n**Generated Title:** ${ticketData.ticket.short_title || 'No title'}\n\n**Generated Problem Description:** ${ticketData.ticket.problem_description || 'No description'}\n\nHow can I help you with this issue?`,
              timestamp: new Date(),
            },
          ]);
        } else {
          // Fallback if ticket details can't be fetched
          setMessages([
            {
              id: 1,
              type: "bot",
              content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,
              timestamp: new Date(),
            },
            {
              id: 2,
              type: "bot",
              content: `🎫 **Ticket Created:** ${ticketNumber}\n\nHow can I help you with your issue?`,
              timestamp: new Date(),
            },
          ]);
        }
      } catch (err) {
        console.error("Failed to fetch ticket details:", err);
        // Fallback messages
        setMessages([
          {
            id: 1,
            type: "bot",
            content: `👋 Welcome, ${name}! Your new support ticket ${ticketNumber} has been created successfully.`,
            timestamp: new Date(),
          },
          {
            id: 2,
            type: "bot",
            content: `🎫 **Ticket Created:** ${ticketNumber}\n\nHow can I help you with your issue?`,
            timestamp: new Date(),
          },
        ]);
      }
    } catch (err) {
      console.error("Failed to load new ticket:", err);
      setError("Failed to load ticket. Please try again.");
    }
  };

  // Load specific pending ticket (when selected from list)
  const loadSpecificPendingTicket = async (ticketNumber) => {
    if (!accessToken) return;

    try {
      // First get user info
      const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!userResponse.ok) {
        throw new Error("Failed to fetch user info");
      }

      const userData = await userResponse.json();
      const name = userData.name || userData.username || userData.email;
      setUserName(name);
      setOrgVerified(true);
      setActiveTicket(ticketNumber);

      // Fetch specific ticket details
      const ticketResponse = await fetch(`${BACKEND_URL}/api/ticket/${ticketNumber}/`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!ticketResponse.ok) {
        throw new Error("Failed to fetch ticket details");
      }

      const ticket = await ticketResponse.json();

      const messages = [
        {
          id: 1,
          type: "bot",
          content: `👋 Welcome back, ${name}! Continuing with your ticket: ${ticketNumber}`,
          timestamp: new Date(),
        },
        {
          id: 2,
          type: "bot",
          content: `📋 **Ticket Details:**\n**Title:** ${ticket.short_title || 'No title'}\n**Problem:** ${ticket.problem_description || 'No description available'}`,
          timestamp: new Date(),
        },
      ];

      // Add previous solution if available
      if (ticket.solution_summary && ticket.solution_summary.trim() !== "" && ticket.solution_summary !== "No solution yet.") {
        messages.push({
          id: 3,
          type: "bot",
          content: `💡 **Previous Solution Summary:**\n${ticket.solution_summary}\n\nDo you have any follow-up questions about this solution?`,
          timestamp: new Date(),
        });
      } else {
        messages.push({
          id: 3,
          type: "bot",
          content: `How can I help you with this ticket?`,
          timestamp: new Date(),
        });
      }

      setMessages(messages);
    } catch (err) {
      console.error("Failed to load specific pending ticket:", err);
      setError("Failed to load ticket. Please try again.");
    }
  };

  // Load pending ticket directly (for pending mode)
  const loadPendingTicketDirectly = async () => {
    if (!accessToken) return;

    try {
      // First get user info
      const userResponse = await fetch(`${BACKEND_URL}/api/user_info/`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!userResponse.ok) {
        throw new Error("Failed to fetch user info");
      }

      const userData = await userResponse.json();
      const name = userData.name || userData.username || userData.email;
      setUserName(name);
      setOrgVerified(true);

      // Fetch pending tickets
      const ticketsResponse = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!ticketsResponse.ok) {
        throw new Error("Failed to fetch pending tickets");
      }

      const ticketsData = await ticketsResponse.json();

      if (ticketsData.tickets && ticketsData.tickets.length > 0) {
        const latestTicket = ticketsData.tickets[0]; // Get the latest ticket
        setActiveTicket(latestTicket.ticket_number);

        // Fetch detailed ticket information including solution summary
        try {
          const detailResponse = await fetch(`${BACKEND_URL}/api/ticket/${latestTicket.ticket_number}/`, {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
          });

          if (detailResponse.ok) {
            const detailData = await detailResponse.json();
            const ticket = detailData;

            const messages = [
              {
                id: 1,
                type: "bot",
                content: `👋 Welcome back, ${name}! Continuing with your ticket: ${latestTicket.ticket_number}`,
                timestamp: new Date(),
              },
              {
                id: 2,
                type: "bot",
                content: `📋 **Ticket Details:**\n**Title:** ${ticket.short_title || 'No title'}\n**Problem:** ${ticket.problem_description || 'No description available'}`,
                timestamp: new Date(),
              },
            ];

            // Add previous solution if available
            if (ticket.solution_summary && ticket.solution_summary.trim() !== "" && ticket.solution_summary !== "No solution yet.") {
              messages.push({
                id: 3,
                type: "bot",
                content: `💡 **Previous Solution Summary:**\n${ticket.solution_summary}\n\nDo you have any follow-up questions about this solution?`,
                timestamp: new Date(),
              });
            } else {
              messages.push({
                id: 3,
                type: "bot",
                content: `How can I help you with this ticket?`,
                timestamp: new Date(),
              });
            }

            setMessages(messages);
          } else {
            // Fallback if detailed ticket info can't be fetched
            setMessages([
              {
                id: 1,
                type: "bot",
                content: `👋 Welcome back, ${name}! Continuing with your ticket: ${latestTicket.ticket_number}`,
                timestamp: new Date(),
              },
              {
                id: 2,
                type: "bot",
                content: `📋 **Ticket Details:**\n**Title:** ${latestTicket.title || latestTicket.short_title || 'No title'}\n**Problem:** ${latestTicket.problem_description || 'No description available'}\n\nHow can I help you with this ticket?`,
                timestamp: new Date(),
              },
            ]);
          }
        } catch (err) {
          console.error("Failed to fetch detailed ticket info:", err);
          // Fallback messages
          setMessages([
            {
              id: 1,
              type: "bot",
              content: `👋 Welcome back, ${name}! Continuing with your ticket: ${latestTicket.ticket_number}`,
              timestamp: new Date(),
            },
            {
              id: 2,
              type: "bot",
              content: `📋 **Ticket Details:**\n**Title:** ${latestTicket.title || latestTicket.short_title || 'No title'}\n**Problem:** ${latestTicket.problem_description || 'No description available'}\n\nHow can I help you with this ticket?`,
              timestamp: new Date(),
            },
          ]);
        }
      } else {
        setMessages([
          {
            id: 1,
            type: "bot",
            content: `👋 Welcome, ${name}! No pending tickets found. Please go back to create a new ticket.`,
            timestamp: new Date(),
          },
        ]);
      }
    } catch (err) {
      console.error("Failed to load pending ticket:", err);
      setError("Failed to load pending ticket. Please try again.");
    }
  };

  // File download handler
  const handleFileDownload = () => {
    // Show list of downloadable files in chat instead of opening directly
    const fileListContent = "Here are the related files you can download:\n\n" +
      pendingFiles.map((file, index) =>
        `${index + 1}. ${file.source_file}`
      ).join('\n');

    // Add message with downloadable file links
    const fileMessage = {
      id: Date.now(),
      type: "bot",
      content: fileListContent,
      timestamp: new Date(),
      downloadableFiles: pendingFiles // Store files for download functionality
    };

    setMessages(prev => [...prev, fileMessage]);
    setPendingFiles(null);

    // Add follow-up question
    addBot("Do you have any more questions about this ticket?");
    setAwaitingOtherQueries(true);
  };

  // Ticket closure handler
  const handleTicketClose = async () => {
    if (!activeTicket) return;

    try {
      const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          ticket_number: activeTicket,
          status: "closed"
        }),
      });

      if (response.ok) {
        addBot(`✅ Ticket ${activeTicket} has been closed successfully. Thank you for using our support system!`);

        // Auto logout after 3 seconds
        setTimeout(() => {
          handleLogout();
        }, 3000);
      } else {
        addBot("❌ Failed to close the ticket. Please try again.");
      }
    } catch (err) {
      console.error("Error closing ticket:", err);
      addBot("❌ Error occurred while closing the ticket. Please try again.");
    }
  };

  // Handle yes/no responses
  const handleYesNoResponse = (response, context) => {
    const userMsg = {
      id: Date.now(),
      type: "user",
      content: response ? "Yes" : "No",
      timestamp: new Date(),
    };
    setMessages((prev) => [...prev, userMsg]);

    if (context === "file_download") {
      if (response && pendingFiles && pendingFiles.length > 0) {
        handleFileDownload();
      } else {
        setPendingFiles(null);
        addBot("Do you have any more questions about this ticket?");
        setAwaitingOtherQueries(true);
      }
    } else if (context === "more_queries") {
      setAwaitingOtherQueries(false);
      if (response) {
        addBot("Please ask your question:");
      } else {
        addBot("Do you want to close this ticket?");
        setAwaitingCloseConfirmation(true);
      }
    } else if (context === "close_ticket") {
      setAwaitingCloseConfirmation(false);
      if (response) {
        handleTicketClose();
      } else {
        addBot("Ticket remains open. How else can I help you?");
      }
    } else if (context === "unrelated_query") {
      setAwaitingUnrelatedQueryResponse(false);
      if (response) {
        // Redirect to new ticket page
        window.location.href = "/new-ticket";
      } else {
        // Ask if they want to close or keep open the ticket
        addBot("Would you like to close this ticket or keep it open?");
        setAwaitingCloseOrKeepOpen(true);
      }
    } else if (context === "close_or_keep_open") {
      setAwaitingCloseOrKeepOpen(false);
      if (response) {
        // Close ticket and logout
        handleTicketClose();
      } else {
        // Keep ticket open and continue
        addBot("Ticket remains open. How else can I help you?");
      }
    }
  };

  // Session keep-alive when buttons are shown
  useEffect(() => {
    const isAwaitingConfirmation = awaitingOtherQueries || awaitingCloseConfirmation || awaitingUnrelatedQueryResponse || awaitingCloseOrKeepOpen || (pendingFiles && pendingFiles.length > 0);

    if (isAwaitingConfirmation) {
      // Start keep-alive interval
      const interval = setInterval(() => {
        if (accessToken) {
          fetch(`${BACKEND_URL}/api/user_info/`, {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
          }).catch(() => {
            // Silently handle errors to prevent disrupting user experience
          });
        }
      }, 30000); // Keep session alive every 30 seconds

      setSessionKeepAlive(interval);

      return () => {
        if (interval) {
          clearInterval(interval);
        }
      };
    } else {
      // Clear keep-alive when no buttons are shown
      if (sessionKeepAlive) {
        clearInterval(sessionKeepAlive);
        setSessionKeepAlive(null);
      }
    }
  }, [awaitingOtherQueries, awaitingCloseConfirmation, awaitingUnrelatedQueryResponse, awaitingCloseOrKeepOpen, pendingFiles, accessToken, sessionKeepAlive]);

  // Cleanup session keep-alive on unmount
  useEffect(() => {
    return () => {
      if (sessionKeepAlive) {
        clearInterval(sessionKeepAlive);
      }
    };
  }, [sessionKeepAlive]);

  // Check if input should be disabled
  const isInputDisabled = () => {
    return awaitingOtherQueries || awaitingCloseConfirmation || awaitingUnrelatedQueryResponse || awaitingCloseOrKeepOpen || (pendingFiles && pendingFiles.length > 0);
  };

  // Input change handler
  const onInputChange = (e) => {
    if (error) setError("");
    setQuery(e.target.value);
  };

  // Dropdown change handler for productType
  const handleProductTypeChange = (e) => {
    const selectedType = e.target.value;
    setTicketData({ ...ticketData, productType: selectedType });
    setMessages((prev) => [
      ...prev,
      {
        id: Date.now(),
        type: "user",
        content: `Selected product type: ${selectedType}`,
        timestamp: new Date(),
      },
    ]);
    setTicketStep(ticketStep + 1);
    addBot(ticketQuestions[ticketStep]);
  };

  // Verify organization name
  const verifyOrganization = async (orgName) => {
    setVerifying(true);
    setError("");

    try {
      const response = await apiPost(`${BACKEND_URL}/api/verify_organization/`, {
        organization: orgName,
      });

      const data = await response.json();

      if (response.ok && data.status === "verified") {
        setOrgVerified(true);
        setUserName(data.name || username);

        setMessages((prev) => [
          ...prev,
          {
            id: Date.now(),
            type: "bot",
            content: data.message || "✅ Organization verified.",
            timestamp: new Date(),
          },
        ]);

        await fetchPendingTickets();
      } else {
        const baseUrl = window.location.origin;
        const signupLink = `[sign up here](${baseUrl}/signup/)`;

        setError(data.message || "❌ Organization mismatch.");
        setMessages((prev) => [
          ...prev,
          {
            id: Date.now(),
            type: "bot",
            content: `❌ Organization verification failed.\n\nIf you belong to a new organization, please ${signupLink} to register first.`,
            timestamp: new Date(),
          },
        ]);

        localStorage.removeItem("access");
      }
    } catch (err) {
      setError("Network error during verification.");
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "bot",
          content: "❌ Network error during organization verification. Please try again.",
          timestamp: new Date(),
        },
      ]);
      console.error("Verification error:", err);
    } finally {
      setVerifying(false);
    }
  };

  // Helper function to fetch pending tickets after verification
  async function fetchPendingTickets() {
    try {
      const response = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      const data = await response.json();

      if (response.ok) {
        setPendingTickets(data.tickets || []);
        if (data.tickets && data.tickets.length > 0) {
          setAwaitingPendingChoice(true);
          addBot(
            `You have ${data.tickets.length} pending ticket(s). Do you want to continue with any of them? (yes/no)`
          );
        } else {
          setAskRaiseTicket(true);
          addBot("No pending tickets found. Would you like to raise a support ticket? (yes/no)");
        }
      } else {
        setPendingTickets([]);
        setAskRaiseTicket(true);
        addBot("Could not fetch pending tickets. Would you like to raise a support ticket? ( Juno/no)");
      }
    } catch (err) {
      console.error("Error fetching pending tickets:", err);
      setPendingTickets([]);
      setAskRaiseTicket(true);
      addBot("Error fetching pending tickets. Would you like to raise a support ticket? (yes/no)");
    }
  }

  // Submit ticket with latest data
  const submitTicket = async (finalTicketData) => {
    setLoading(true);
    setError("");

    const validProductTypes = ["Camera", "Frame Grabber", "Accessories", "Software"];
    const allFilled = Object.values(finalTicketData).every(
      (v) => v && v.trim() !== ""
    );
    if (!allFilled) {
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "bot",
          content: "❌ Please fill in all required fields before submitting the ticket.",
          timestamp: new Date(),
        },
      ]);
      setLoading(false);
      return;
    }
    if (!validProductTypes.includes(finalTicketData.productType)) {
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "bot",
          content: "❌ Invalid product type. Please select: Camera, Frame Grabber, Accessories, or Software.",
          timestamp: new Date(),
        },
      ]);
      setLoading(false);
      return;
    }

    try {
      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          product_type: finalTicketData.productType,
          purchased_from: finalTicketData.purchasedFrom,
          year_of_purchase: finalTicketData.yearOfPurchase,
          product_name: finalTicketData.productName,
          model: finalTicketData.model,
          serial_no: finalTicketData.serialNo,
          operating_system: finalTicketData.operatingSystem,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setCurrentTicketNumber(data.ticket_number);
        setActiveTicket(data.ticket_number);
        setAwaitingProblemDescription(true);
        setMessages((prev) => [
          ...prev,
          {
            id: Date.now(),
            type: "bot",
            content: `🎉 Thank you! Your support ticket has been created successfully. Your ticket number is **${data.ticket_number}**.\n\nPlease describe your problem so we can assist you.`,
            timestamp: new Date(),
          },
        ]);
      } else {
        setMessages((prev) => [
          ...prev,
          {
            id: Date.now(),
            type: "bot",
            content: `❌ Failed to create ticket: ${JSON.stringify(data.errors || data.message)}`,
            timestamp: new Date(),
          },
        ]);
      }
    } catch (err) {
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "bot",
          content: `❌ Network error while creating ticket: ${err.message}`,
          timestamp: new Date(),
        },
      ]);
    } finally {
      setLoading(false);
      setTicketStep(0);
      setAskRaiseTicket(false);
      setTicketData({
        productType: "",
        purchasedFrom: "",
        yearOfPurchase: "",
        productName: "",
        model: "",
        serialNo: "",
        operatingSystem: "",
      });
    }
  };

  // Helper to add a bot message
  function addBot(text) {
    setMessages((prev) => [
      ...prev,
      { id: Date.now(), type: "bot", content: text, timestamp: new Date() },
    ]);
  }

  // Handle ticket selection from UI
  const handleTicketSelect = async (ticketNumber) => {
    setAwaitingTicketSelect(false);
    setActiveTicket(ticketNumber);
    setCurrentTicketNumber(ticketNumber);
    setShowTickets(false);

    try {
      const summaryRes = await fetch(`${BACKEND_URL}/api/ticket_summary/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({ ticket_number: ticketNumber }),
      });

      const summaryData = await summaryRes.json();

      if (summaryRes.ok) {
        addBot(
          `🔄 Resuming ticket **${ticketNumber}** …\n\n` +
            `📝 **Raised problem:** ${summaryData.problem_summary || summaryData.problem_description}\n\n` +
            `💡 **Given solution:** ${summaryData.solution_summary || "No solution yet."}\n\n` +
            "✅ You can ask your follow-up query now."
        );
      } else {
        addBot("⚠️ Error fetching ticket summary.");
      }
    } catch (err) {
      addBot("❌ Network error while fetching ticket summary.");
    }
  };

  // FULLY UPDATED handleSubmit
async function handleSubmit(e) {
  e.preventDefault();
  if (!query.trim() || loading || verifying) return;

  const currentQuery = query.trim().toLowerCase();

  // --- New: Check 5 queries per ticket limit ---
  if (
    activeTicket &&            // There is an active ticket
    ticketStep === 0 &&        // Not in ticket creation steps
    !awaitingProblemDescription && // Not waiting for problem description input
    ticketQueryCount >= 100   // Limit reached
  ) {
    addBot(
      "🛑 You have reached the maximum of five queries for this ticket. It has been automatically escalated to ensure prompt resolution. Kindly create a new ticket for any further inquiries or await our team’s response."
    );
    setQuery("");
    return;
  }
  // ---------------------------------------------

  if (ticketRefused) {
    if (queriesAfterNoTicket >= MAX_QUERIES_AFTER_NO_TICKET) {
      addBot(
        "⚠️ You have reached the maximum number of free queries. Please raise a support ticket for further assistance."
      );
      setQuery("");
      return;
    } else {
      setQueriesAfterNoTicket((n) => n + 1);
    }
  }

  const userMsg = {
    id: Date.now(),
    type: "user",
    content: query.trim(),
    timestamp: new Date(),
  };
  setMessages((prev) => [...prev, userMsg]);
  setQuery("");
  setError("");

  if (awaitingPendingChoice) {
    setAwaitingPendingChoice(false);

    if (currentQuery === "yes") {
      setAwaitingTicketSelect(true);
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "bot",
          content: `Select a ticket by typing its number:`,
          timestamp: new Date(),
          tickets: pendingTickets.map((t, i) => ({
            index: i + 1,
            ticketNumber: t.ticket_number,
            title: t.title || t.short_title || "No title",
          })),
        },
      ]);
    } else if (currentQuery === "no") {
      setAskRaiseTicket(true);
      addBot("Do you want to raise a support ticket? (yes/no)");
    } else {
      addBot("Please answer 'yes' or 'no'. Do you want to continue an open ticket?");
      setAwaitingPendingChoice(true);
    }
    return;
  }

  if (awaitingTicketSelect) {
    const picked = pendingTickets.find(
      (t, idx) =>
        currentQuery === String(idx + 1) ||
        currentQuery.includes(t.ticket_number.toLowerCase())
    );

    if (!picked) {
      addBot("Ticket not recognised, please type its number.");
      return;
    }

    await handleTicketSelect(picked.ticket_number);
    return;
  }

  if (!orgVerified) {
    await verifyOrganization(currentQuery);
    return;
  }

  if (askRaiseTicket) {
    if (currentQuery === "yes") {
      setAskRaiseTicket(false);
      setTicketStep(1);
      addBot(ticketQuestions[0]);
      setTicketRefused(false);
      setQueriesAfterNoTicket(0);

      // --- New: Reset query count when new ticket starts ---
      setTicketQueryCount(0);
      // ------------------------------------------------------

    } else if (currentQuery === "no") {
      setAskRaiseTicket(false);
      addBot("👍 Okay, no ticket will be raised. How else can I help you?");
      setTicketRefused(true);
      setQueriesAfterNoTicket(0);
    } else {
      addBot("Please answer 'yes' or 'no'. Do you want to raise a support ticket?");
    }
    return;
  }

  if (awaitingUnrelatedQueryResponse) {
    setAwaitingUnrelatedQueryResponse(false);
    if (currentQuery === "yes") {
      setTicketStep(1);
      setActiveTicket(null);
      setCurrentTicketNumber(null);
      addBot(ticketQuestions[0]);

      // --- New: Reset query count when new ticket starts here too ---
      setTicketQueryCount(0);
      // ---------------------------------------------------------------

    } else if (currentQuery === "no") {
      setAwaitingCloseConfirmation(true);
      addBot("Can I close this ticket now? (yes/no)");
    } else {
      setAwaitingUnrelatedQueryResponse(true);
      addBot("Please answer 'yes' or 'no'. Do you want to create a new ticket?");
    }
    return;
  }

  if (ticketStep > 0 && ticketStep <= ticketQuestions.length) {
    if (ticketStep === 1) {
      const selectedType = query.trim();
      if (productTypeOptions.includes(selectedType)) {
        setTicketData({ ...ticketData, productType: selectedType });
        setMessages((prev) => [
          ...prev,
          {
            id: Date.now(),
            type: "user",
            content: `Selected product type: ${selectedType}`,
            timestamp: new Date(),
          },
        ]);
        setTicketStep(ticketStep + 1);
        addBot(ticketQuestions[ticketStep]);
      } else {
        addBot(
          "Please select a valid product type from: Camera, Frame Grabber, Accessories, or Software."
        );
      }
      return;
    }
    const keys = [
      "purchasedFrom",
      "yearOfPurchase",
      "productName",
      "model",
      "serialNo",
      "operatingSystem",
    ];
    const currentField = keys[ticketStep - 2];
    const updatedTicketData = { ...ticketData, [currentField]: query.trim() };
    setTicketData(updatedTicketData);

    if (ticketStep < ticketQuestions.length) {
      setTicketStep(ticketStep + 1);
      addBot(ticketQuestions[ticketStep]);
    } else {
      await submitTicket(updatedTicketData);
    }
    return;
  }

  if (awaitingProblemDescription && currentTicketNumber) {
    setAwaitingProblemDescription(false);
    setLoading(true);

    try {
      // Use the chat endpoint directly - it will handle problem description collection
      const response = await apiPost(`${BACKEND_URL}/api/chat/`, {
        query: userMsg.content,
        ticket_mode: true,
        ticket_id: currentTicketNumber,
      });

      const saveData = await response.json();
      if (!response.ok) {
        throw new Error(saveData.error || "Failed to save problem description.");
      }

      let botContent = saveData.answer || "No solution available at the moment.";

      const rawFiles = saveData.files ?? []; // Updated from saveData.related_files
      const validFiles = rawFiles.filter((f) => {
        if (typeof f === "string") {
          return f.trim() !== "" && !f.toLowerCase().startsWith("none");
        }
        if (typeof f === "object" && f !== null && f.filename) {
          return (
            f.filename.trim() !== "" &&
            !f.filename.toLowerCase().startsWith("none")
          );
        }
        return false;
      });

      if (validFiles.length > 0) {
        setPendingFiles(
          validFiles.map((f) => {
            const filename = typeof f === "string" ? f : f.filename;
            const url =
              typeof f === "string" || !f.url || !f.url.startsWith("http")
                ? `${BACKEND_URL}/api/files/${encodeURIComponent(
                    filename
                  )}?token=${accessToken}`
                : `${f.url}${f.url.includes("?") ? "&" : "?"}token=${accessToken}`;
            return { source_file: filename, url };
          })
        );
        botContent += "\n\n💡 For full explanation, do you want the related file? (yes/no)";
      } else {
        setPendingFiles(null);
      }

      setMessages((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "bot",
          content: botContent,
          timestamp: new Date(),
        },
      ]);

      setAwaitingOtherQueries(true);
    } catch (err) {
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "bot",
          content: `❌ Error processing problem description: ${err.message}`,
          timestamp: new Date(),
        },
      ]);
    } finally {
      setLoading(false);
    }
    return;
  }

  if (pendingFiles && (currentQuery === "yes" || currentQuery === "no")) {
    const baseMessages = [];

    if (currentQuery === "yes") {
      const downloadLinks = pendingFiles
        .map((f, idx) => `${idx + 1}. [${f.source_file}](${f.url})`)
        .join("\n");

      baseMessages.push({
        id: Date.now() + 1,
        type: "bot",
        content: `📎 Here are the related files:\n\n${downloadLinks}`,
        timestamp: new Date(),
      });
    } else {
      baseMessages.push({
        id: Date.now() + 1,
        type: "bot",
        content: "👍 Okay, no files will be sent.",
        timestamp: new Date(),
      });
    }

    if (activeTicket !== null) {
      baseMessages.push({
        id: Date.now() + 2,
        type: "bot",
        content: "Do you have any other queries? (yes/no)",
        timestamp: new Date(),
      });
      setAwaitingOtherQueries(true);
    }

    setMessages((prev) => [...prev, ...baseMessages]);
    setPendingFiles(null);
    return;
  }

  if (awaitingOtherQueries) {
    if (currentQuery === "no") {
      setAwaitingOtherQueries(false);
      setAwaitingCloseConfirmation(true);
      addBot("Can I close this ticket now? (yes/no)");
    } else if (currentQuery === "yes") {
      setAwaitingOtherQueries(false);
      addBot("Please go ahead and ask your question.");
    } else {
      addBot("Please answer 'yes' or 'no'. Do you have any other queries?");
    }
    return;
  }

  if (awaitingCloseConfirmation) {
    if (currentQuery === "yes") {
      setAwaitingCloseConfirmation(false);
      setLoading(true);
      try {
        const response = await fetch(`${BACKEND_URL}/api/update_ticket_status/`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          body: JSON.stringify({
            ticket_number: currentTicketNumber,
            status: "closed",
          }),
        });
        const data = await response.json();

        if (response.ok) {
          setActiveTicket(null);
          setCurrentTicketNumber(null);
          setTicketData({
            productType: "",
            purchasedFrom: "",
            yearOfPurchase: "",
            productName: "",
            model: "",
            serialNo: "",
            operatingSystem: "",
          });

          addBot(`✅ Ticket ${currentTicketNumber} has been closed. Thank you!`);

          // --- New: Reset query count when ticket is closed ---
          setTicketQueryCount(0);
          // -----------------------------------------------------

        } else {
          throw new Error(data.error || "Failed to close ticket.");
        }
      } catch (err) {
        addBot(`❌ Error closing ticket: ${err.message}`);
      } finally {
        setLoading(false);
      }
    } else if (currentQuery === "no") {
      setAwaitingCloseConfirmation(false);
      addBot("Okay, ticket will remain open.");
    } else {
      addBot("Please answer 'yes' or 'no'. Can I close this ticket now?");
    }
    return;
  }

  setLoading(true);
  try {
    const historyText = messages
      .filter((m) => m.type === "user" || m.type === "bot")
      .map((m) => `${m.type === "user" ? "User" : "Bot"}: ${m.content}`)
      .join("\n");

    const finalPrompt = promptTemplate
      ? promptTemplate
          .replace("{context_text}", "some context text here")
          .replace("{history_text}", historyText)
          .replace("{query}", query.trim())
      : query.trim();

    const response = await apiPost(`${BACKEND_URL}/api/chat/`, {
      query: finalPrompt,
      ticket_mode: !!activeTicket,
      ticket_id: activeTicket,
      stage: awaitingUnrelatedQueryResponse ? "unrelated_query" : "",
    });

    const data = await response.json();

    if (response.ok) {
      let botContent = data.answer || "…";

      if (data.stage === "unrelated_query") {
        setAwaitingUnrelatedQueryResponse(true);
      } else if (data.stage === "create_new_ticket") {
        setTicketStep(1);
        setActiveTicket(null);
        setCurrentTicketNumber(null);
        addBot(ticketQuestions[0]);

        // --- New: Reset query count on new ticket here too ---
        setTicketQueryCount(0);
        // ------------------------------------------------------
        return;
      }

      const rawFiles = data.files ?? [];
      const validFiles = rawFiles.filter((f) => {
        if (typeof f === "string") {
          return f.trim() !== "" && !f.toLowerCase().startsWith("none");
        }
        if (typeof f === "object" && f !== null && f.filename) {
          return (
            f.filename.trim() !== "" &&
            !f.filename.toLowerCase().startsWith("none")
          );
        }
        return false;
      });

      if (validFiles.length > 0) {
        setPendingFiles(
          validFiles.map((f) => {
            const filename = typeof f === "string" ? f : f.filename;
            const url =
              typeof f === "string" || !f.url || !f.url.startsWith("http")
                ? `${BACKEND_URL}/api/files/${encodeURIComponent(filename)}?token=${accessToken}`
                : `${f.url}${f.url.includes("?") ? "&" : "?"}token=${accessToken}`;
            return {
              source_file: filename,
              url: url,
            };
          })
        );
        if (!botContent.toLowerCase().includes("do you want the related file")) {
          botContent += "\n\n💡 For full explanation, do you want the related file? (yes/no)";
        }
      } else {
        setPendingFiles(null);
      }

      setMessages((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "bot",
          content: botContent,
          timestamp: new Date(),
        },
      ]);

      // --- New: Increment query count for ticket queries ---
      if (activeTicket && ticketStep === 0) {
        setTicketQueryCount((prev) => prev + 1);
      }
      // ------------------------------------------------------

      if (data.stage === "await_close") {
        setAwaitingCloseConfirmation(true);
      }
    } else {
      setError(data.error || "Error processing request");
    }
  } catch (err) {
    setError("Network error: " + err.message);
  } finally {
    setLoading(false);
  }
}


  // Logout handler
  function handleLogout() {
    localStorage.removeItem("access");
    localStorage.removeItem("refresh");
    localStorage.removeItem("userData");
    window.location.href = "/auth";
  }

  // Format message time HH:MM
  const formatTime = (timestamp) => {
    return timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  // Render chat messages with links parsed
  const renderMessages = () =>
    messages.map((message) => (
      <div
        key={message.id}
        className={`message ${message.type}`}
        style={{ textAlign: message.type === "user" ? "right" : "left" }}
        aria-live="polite"
      >
        <div
          className="message-content"
          style={{
            display: "inline-block",
            maxWidth: "75%",
            padding: "12px 16px",
            borderRadius: "1rem",
            backgroundColor: message.type === "user" ? "#3B82F6" : "rgba(255, 255, 255, 0.95)",
            color: message.type === "user" ? "white" : "#333",
            border: message.type === "user" ? "2px solid #1E40AF" : "2px solid #E5E7EB",
            boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
          }}
        >
          <div
            className="message-text"
            style={{
              whiteSpace: "pre-wrap",
              overflowY: "auto",
              maxHeight: "400px",
              fontSize: "1em",
              lineHeight: "1.5",
            }}
          >
            {message.content.split("\n").map((line, idx) => {
              const parts = [];
              let remaining = line;
              let keyIndex = 0;

              while (remaining.length > 0) {
                const linkMatch = remaining.match(/\[(.*?)\]\((http.*?)\)/);
                const boldMatch = remaining.match(/\*\*(.*?)\*\*/);

                if (linkMatch && (!boldMatch || linkMatch.index < boldMatch.index)) {
                  let href = linkMatch[2];
                  if (!href.startsWith("http")) {
                    href = `${BACKEND_URL}${href.startsWith("/") ? href : "/" + href}`;
                  }
                  if (
                    (href.startsWith(`${BACKEND_URL}/api/files/`) || href.startsWith("/api/files/")) &&
                    accessToken &&
                    !href.includes("token=")
                  ) {
                    href += href.includes("?") ? `&token=${accessToken}` : `?token=${accessToken}`;
                  }
                  parts.push(
                    <span key={keyIndex++}>
                      {remaining.slice(0, linkMatch.index)}
                      <a
                        href={href}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{ color: "#0645AD", textDecoration: "underline" }}
                      >
                        {linkMatch[1]}
                      </a>
                    </span>
                  );
                  remaining = remaining.slice(linkMatch.index + linkMatch[0].length);
                } else if (boldMatch) {
                  parts.push(
                    <span key={keyIndex++}>
                      {remaining.slice(0, boldMatch.index)}
                      <strong>{boldMatch[1]}</strong>
                    </span>
                  );
                  remaining = remaining.slice(boldMatch.index + boldMatch[0].length);
                } else {
                  parts.push(<span key={keyIndex++}>{remaining}</span>);
                  break;
                }
              }

              return <div key={idx}>{parts}</div>;
            })}
            {message.tickets && (
              <div style={{ marginTop: "8px" }}>
                {message.tickets.map((ticket, idx) => (
                  <div
                    key={ticket.ticketNumber}
                    style={{
                      cursor: "pointer",
                      padding: "6px 4px",
                      borderBottom:
                        idx !== message.tickets.length - 1 ? "1px solid #eee" : "none",
                    }}
                    onClick={() => handleTicketSelect(ticket.ticketNumber)}
                  >
                    {ticket.index}. <strong>{ticket.ticketNumber}</strong> — {ticket.title}
                  </div>
                ))}
              </div>
            )}
            {message.downloadableFiles && (
              <div style={{ marginTop: "8px" }}>
                {message.downloadableFiles.map((file, idx) => (
                  <div
                    key={idx}
                    style={{
                      cursor: "pointer",
                      padding: "6px 4px",
                      borderBottom:
                        idx !== message.downloadableFiles.length - 1 ? "1px solid #eee" : "none",
                      color: "#0645AD",
                      textDecoration: "underline",
                    }}
                    onClick={() => {
                      // Create a temporary link to download the file
                      const link = document.createElement('a');
                      link.href = file.url;
                      link.download = file.source_file;
                      link.target = '_blank';
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }}
                  >
                    {idx + 1}. {file.source_file}
                  </div>
                ))}
              </div>
            )}
          </div>
          <div
            className="message-time"
            style={{ fontSize: "0.7em", color: "#666", marginTop: "6px" }}
          >
            {formatTime(message.timestamp)}
          </div>
        </div>
      </div>
    ));

  return (
    <div className="chat-container" role="main" aria-label="AI Agent Chatbot">
      <div className="chat-header" style={{ position: "relative" }}>
        <h1>ONLINE SOLUTIONS TECHNICAL SUPPORT</h1>
        <p className="subtitle">Ask questions — and get the answers they were meant to give</p>
        {username && (
          <p style={{ fontSize: "0.9em", color: "black" }}>
            Logged in as: <strong>{username}</strong>
          </p>
        )}
        <div style={{ position: "absolute", top: 15, right: 15 }}>
          <button
            onClick={handleLogout}
            style={{
              padding: "6px 12px",
              cursor: "pointer",
              backgroundColor: "#d9534f",
              border: "none",
              borderRadius: 4,
              color: "white",
              fontWeight: "bold",
            }}
            aria-label="Logout"
            title="Logout"
          >
            Logout
          </button>
        </div>
      </div>

      {/* CHAT MESSAGES */}
      <div
        className="chat-messages"
        aria-live="polite"
        aria-relevant="additions"
        style={{
          flex: 1,
          overflowY: "auto",
          padding: "10px",
          scrollBehavior: "smooth"
        }}
      >
        {renderMessages()}
        {(loading || verifying) && (
          <div className="message bot typing" aria-label="Analyzing">
            <div className="message-content">
              <div className="typing-indicator" aria-hidden="true">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <div className="message-text">
                {verifying ? "Verifying organization..." : "Analyzing..."}
              </div>
            </div>
          </div>
        )}

        {/* Yes/No Buttons for File Download */}
        {pendingFiles && pendingFiles.length > 0 && (
          <div style={{ padding: "10px", textAlign: "center" }}>
            <YesNoButtons
              onYes={() => handleYesNoResponse(true, "file_download")}
              onNo={() => handleYesNoResponse(false, "file_download")}
              yesText="Download File"
              noText="Skip"
            />
          </div>
        )}

        {/* Yes/No Buttons for More Queries */}
        {awaitingOtherQueries && (
          <div style={{ padding: "10px", textAlign: "center" }}>
            <YesNoButtons
              onYes={() => handleYesNoResponse(true, "more_queries")}
              onNo={() => handleYesNoResponse(false, "more_queries")}
              yesText="Yes"
              noText="No"
            />
          </div>
        )}

        {/* Yes/No Buttons for Ticket Closure */}
        {awaitingCloseConfirmation && (
          <div style={{ padding: "10px", textAlign: "center" }}>
            <YesNoButtons
              onYes={() => handleYesNoResponse(true, "close_ticket")}
              onNo={() => handleYesNoResponse(false, "close_ticket")}
              yesText="Close Ticket"
              noText="Keep Open"
            />
          </div>
        )}

        {/* Yes/No Buttons for Unrelated Query */}
        {awaitingUnrelatedQueryResponse && (
          <div style={{ padding: "10px", textAlign: "center" }}>
            <YesNoButtons
              onYes={() => handleYesNoResponse(true, "unrelated_query")}
              onNo={() => handleYesNoResponse(false, "unrelated_query")}
              yesText="Yes"
              noText="No"
            />
          </div>
        )}

        {/* Yes/No Buttons for Close or Keep Open */}
        {awaitingCloseOrKeepOpen && (
          <div style={{ padding: "10px", textAlign: "center" }}>
            <YesNoButtons
              onYes={() => handleYesNoResponse(true, "close_or_keep_open")}
              onNo={() => handleYesNoResponse(false, "close_or_keep_open")}
              yesText="Close Ticket"
              noText="Keep Open"
            />
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {error && (
        <div
          className="error-message"
          onClick={() => setError("")}
          style={{ cursor: "pointer" }}
          role="alert"
          aria-live="assertive"
          tabIndex={0}
        >
          {error} (click to dismiss)
        </div>
      )}



      <form className="chat-input-form" onSubmit={handleSubmit} aria-label="Send message form">
        <div className="input-container" style={{ display: "flex", alignItems: "center", width: "100%" }}>
          <input
            type="text"
            value={query}
            onChange={onInputChange}
            placeholder={
              orgVerified
                ? ticketStep > 0
                  ? ticketQuestions[ticketStep - 1]
                  : isInputDisabled()
                  ? ""
                  : "Type your question here..."
                : "Enter your organization name..."
            }
            disabled={loading || verifying || (ticketStep === 1 && !query) || isInputDisabled()}
            autoFocus
            aria-label="Chat input"
            style={{
              flex: "1",
              width: "100%",
              padding: "8px",
              borderRadius: "4px 0 0 4px",
              border: "1px solid #ccc",
              margin: 0,
              backgroundColor: isInputDisabled() ? "#f5f5f5" : "white",
              color: isInputDisabled() ? "#999" : "black",
              cursor: isInputDisabled() ? "not-allowed" : "text",
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
          />
          {ticketStep === 1 && (
            <select
              value={ticketData.productType}
              onChange={handleProductTypeChange}
              style={{
                marginLeft: "0",
                padding: "6px",
                borderRadius: "0",
                border: "1px solid #ccc",
                borderLeft: "none",
                fontSize: "1em",
              }}
              aria-label="Select product type"
            >
              <option value="" disabled>
                Select a product type
              </option>
              {productTypeOptions.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          )}
          <button
            type="submit"
            disabled={loading || !query.trim() || verifying || (ticketStep === 1 && !ticketData.productType)}
            title={loading || verifying ? "Please wait..." : "Send"}
            aria-label="Send message"
            style={{ padding: "8px 12px", borderRadius: "0 4px 4px 0", border: "1px solid #ccc", borderLeft: "none", backgroundColor: "#4CAF50", color: "white" }}
          >
            {loading || verifying ? <span className="spinner" /> : "📤"}
          </button>
        </div>
      </form>
    </div>
  );
}
