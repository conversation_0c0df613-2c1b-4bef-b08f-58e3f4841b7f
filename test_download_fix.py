#!/usr/bin/env python3
"""
Test script to verify the chatbot download logic fix.
This script tests that the correct file corresponding to retrieved content 
is offered to the user instead of random/unrelated files.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_chatbot_backend.settings')
django.setup()

from chatbot.models import PdfFile
from chatbot.views import search_similar_chunks_weaviate
from collections import defaultdict

def test_download_mapping():
    """Test that source_file from Weaviate chunks correctly maps to database entries."""
    
    print("🧪 Testing Download Logic Fix")
    print("=" * 50)
    
    # Test query
    test_query = "What is Falcon4-CLHS?"
    
    print(f"📝 Test Query: {test_query}")
    print()
    
    # Simulate the retrieval process
    try:
        matches = search_similar_chunks_weaviate(test_query, limit=5)
        
        if not matches:
            print("❌ No matches found from Weaviate")
            return
            
        print(f"✅ Found {len(matches)} matches from Weaviate")
        print()
        
        # Extract file scores (same logic as in views.py)
        file_scores = defaultdict(float)
        for match in matches:
            source_file = match.get("source_file")
            certainty = match.get("_additional", {}).get("certainty", 0.0)
            if source_file and certainty:
                file_scores[source_file] += certainty
        
        print("📊 File Scores:")
        for filename, score in sorted(file_scores.items(), key=lambda x: x[1], reverse=True):
            print(f"  {filename}: {score:.3f}")
        print()
        
        # Test the new mapping logic
        print("🔗 Testing Database Mapping:")
        unique_source_files = set()
        for match in matches:
            source_file = match.get("source_file")
            if source_file and source_file in file_scores:
                unique_source_files.add(source_file)
        
        file_objs = []
        for source_filename in unique_source_files:
            try:
                # Look up the file in the database by filename
                pdf_file = PdfFile.objects.get(file_name=source_filename)
                file_objs.append({
                    "filename": source_filename,
                    "url": f"/api/download-pdf/{pdf_file.id}/",
                    "pdf_id": pdf_file.id,
                    "score": file_scores.get(source_filename, 0.0)
                })
                print(f"  ✅ {source_filename} -> ID {pdf_file.id} -> /api/download-pdf/{pdf_file.id}/")
            except PdfFile.DoesNotExist:
                print(f"  ❌ {source_filename} -> NOT FOUND in database")
                continue
        
        # Sort by score
        file_objs.sort(key=lambda x: x['score'], reverse=True)
        
        print()
        print("📎 Final Download Offers (sorted by relevance):")
        if file_objs:
            for i, file_obj in enumerate(file_objs, 1):
                print(f"  {i}. {file_obj['filename']} -> [Download]({file_obj['url']}) (score: {file_obj['score']:.3f})")
        else:
            print("  No valid download offers (all source files missing from database)")
        
        print()
        print("🎯 Test Results:")
        print(f"  - Retrieved chunks: {len(matches)}")
        print(f"  - Unique source files: {len(unique_source_files)}")
        print(f"  - Valid download offers: {len(file_objs)}")
        print(f"  - Missing from database: {len(unique_source_files) - len(file_objs)}")
        
        if file_objs:
            print("  ✅ SUCCESS: Correct files mapped to download links")
        else:
            print("  ⚠️ WARNING: No valid download offers generated")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

def test_database_files():
    """Show available files in database for reference."""
    print("\n📚 Available Files in Database:")
    print("-" * 30)
    
    pdf_files = PdfFile.objects.all()
    if pdf_files:
        for pdf_file in pdf_files:
            print(f"  ID {pdf_file.id}: {pdf_file.file_name}")
    else:
        print("  No PDF files found in database")

if __name__ == "__main__":
    test_database_files()
    test_download_mapping()
