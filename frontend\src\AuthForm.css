/* Full-page container */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(to bottom right, #1E3A8A, #3B82F6);
  font-family: "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* Auth box */
.auth-card {
  width: 100%;
  max-width: 420px;
  background: rgba(255, 255, 255, 0.95);
  padding: 36px 40px;
  border-radius: 1.5rem;
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  animation: fadeIn 0.6s ease;
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  overflow-y: auto; /* scroll if needed */
}

/* Title */
.auth-title {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  color: #1E3A8A;
  margin-bottom: 28px;
}

/* Error banner */
.auth-error {
  background: #ffe8e6;
  border: 1px solid #ffb4b0;
  color: #d32f2f;
  padding: 10px 14px;
  border-radius: 8px;
  font-size: 0.875rem;
  text-align: center;
  margin-bottom: 20px;
}

/* Inputs */
.auth-input {
  width: 100%;
  padding: 14px 16px;
  margin-bottom: 18px;
  font-size: 1rem;
  border: 2px solid #E5E7EB;
  border-radius: 0.75rem;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  background-color: #ffffff;
  box-sizing: border-box;
}

.auth-input:focus {
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Submit button */
.auth-button {
  width: 100%;
  padding: 14px;
  margin-top: 10px;
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  background: #2563EB;
  border: none;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.auth-button:hover:not([disabled]) {
  background: #1E40AF;
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.auth-button:active:not([disabled]) {
  transform: translateY(0);
}

.auth-button[disabled] {
  background: #9CA3AF;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Fieldset */
.auth-fieldset {
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
}

/* Bottom switch text */
.auth-switch {
  margin-top: auto;
  text-align: center;
  font-size: 0.9rem;
  color: #555b6e;
  padding-top: 18px;
}

/* Switch link as button */
.auth-switch-link {
  background: none;
  border: none;
  color: #2563EB;
  cursor: pointer;
  font-weight: 600;
  text-decoration: underline;
  margin-left: 4px;
  transition: color 0.2s;
}

.auth-switch-link:hover {
  color: #1E3A8A;
}

/* Smooth fade animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(12px); }
  to   { opacity: 1; transform: translateY(0);   }
}

/* Mobile responsive scroll - only scroll if viewport height <= 700px */
@media (max-height: 700px) {
  .auth-card {
    max-height: 90vh;
  }
}
