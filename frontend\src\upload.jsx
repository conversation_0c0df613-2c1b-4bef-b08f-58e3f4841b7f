import React, { useState } from 'react';
import axios from 'axios';
import './upload.css'; // Make sure to create this CSS file

const UploadPDF = () => {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploadStatus, setUploadStatus] = useState("");
  const [cameraType, setCameraType] = useState("");

  const handleFileChange = (event) => {
    const newFiles = Array.from(event.target.files);
    const uniqueNewFiles = newFiles.filter(file =>
      !selectedFiles.some(f => f.name === file.name)
    );
    setSelectedFiles(prev => [...prev, ...uniqueNewFiles]);
    setUploadStatus("");
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      setUploadStatus("⚠️ Please select PDF files first.");
      return;
    }

    if (!cameraType) {
      setUploadStatus("⚠️ Please select a camera type.");
      return;
    }

    const formData = new FormData();
    selectedFiles.forEach(file => {
      formData.append("pdf_file", file);
    });
    formData.append("camera_type", cameraType);

    try {
      const response = await axios.post('http://localhost:8000/api/upload_pdf/', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      if (response.status === 200) {
        setUploadStatus("✅ All files uploaded successfully.");
        setSelectedFiles([]);
        setCameraType("");
      } else {
        setUploadStatus("⚠️ Upload failed. Try again.");
      }
    } catch (error) {
      console.error("Upload error:", error);
      setUploadStatus("❌ Error uploading files.");
    }
  };

  const handleRemoveFile = (index) => {
    const updatedFiles = [...selectedFiles];
    updatedFiles.splice(index, 1);
    setSelectedFiles(updatedFiles);
  };

  return (
    <div style={{
      minHeight: "100vh",
      background: "linear-gradient(to bottom right, #1E3A8A, #3B82F6)",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      padding: "2rem"
    }}>
      <div className="upload-container">
        <h1>📄 PDF Upload Manager</h1>

      <label className="upload-label">Camera Type:</label>
      <select
        value={cameraType}
        onChange={(e) => setCameraType(e.target.value)}
        className="upload-input"
        style={{ marginBottom: '20px' }}
      >
        <option value="">Select Camera Type</option>
        <option value="area_scan">Area Scan</option>
        <option value="line_scan">Line Scan</option>
      </select>

      <label className="upload-label">Select PDF Files:</label>
      <input type="file" accept=".pdf" multiple onChange={handleFileChange} className="upload-input" />

      {selectedFiles.length > 0 && (
        <div className="file-list">
          <h4>🗂️ Selected Files:</h4>
          <ul>
            {selectedFiles.map((file, index) => (
              <li key={index}>
                {file.name}
                <button onClick={() => handleRemoveFile(index)} className="remove-btn">❌ Remove</button>
              </li>
            ))}
          </ul>
        </div>
      )}

      <button onClick={handleUpload} className="upload-button" disabled={selectedFiles.length === 0 || !cameraType}>
        🚀 Upload Files
      </button>

        {uploadStatus && <p className="upload-status">{uploadStatus}</p>}
      </div>
    </div>
  );
};

export default UploadPDF;
