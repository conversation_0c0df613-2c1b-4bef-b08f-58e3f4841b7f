import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function ProblemCategoriesPage({ token }) {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const ticketNumber = searchParams.get("ticket");
  const accessToken = token || localStorage.getItem("access");
  
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const problemCategories = [
    "Detection",
    "Acquisition & Triggering", 
    "Frame Rate Issue",
    "Software SDK Issues",
    "Focus & Exposure",
    "Other"
  ];

  useEffect(() => {
    if (!ticketNumber) {
      navigate("/actions");
    }
  }, [ticketNumber, navigate]);

  const handleCategoryToggle = (category) => {
    setSelectedCategories(prev => {
      if (prev.includes(category)) {
        return prev.filter(c => c !== category);
      } else {
        return [...prev, category];
      }
    });
    if (error) setError("");
  };

  const handleOkayClick = async () => {
    if (selectedCategories.length === 0) {
      setError("Please select at least one problem category.");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`${BACKEND_URL}/api/update_ticket_categories/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          ticket_number: ticketNumber,
          problem_categories: selectedCategories,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Redirect directly to chatbot - problem description will be collected in chat
        navigate(`/chatbot/${ticketNumber}`);
      } else {
        setError(data.message || "Failed to save categories. Please try again.");
      }
    } catch (err) {
      console.error("Error saving categories:", err);
      setError("Network error. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: "100vh",
      background: "linear-gradient(to bottom right, #1E3A8A, #3B82F6)",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      padding: "2rem",
      fontFamily: "Arial, sans-serif"
    }}>
      <div style={{
        background: "rgba(255, 255, 255, 0.95)",
        borderRadius: "1.5rem",
        padding: "3rem",
        maxWidth: "700px",
        width: "100%",
        boxShadow: "0 20px 25px rgba(0, 0, 0, 0.25)",
        backdropFilter: "blur(10px)",
        maxHeight: "90vh",
        overflowY: "auto"
      }}>
        <h1 style={{
          color: "#1E3A8A",
          marginBottom: "1.5rem",
          textAlign: "center",
          fontSize: "2rem",
          fontWeight: "600"
        }}>
          Problem Categories
        </h1>

        <p style={{
          fontSize: "1.2rem",
          color: "#4B5563",
          marginBottom: "2rem",
          textAlign: "center",
          lineHeight: "1.6"
        }}>
          To help us assist you better, please select one or more problem categories that apply:
        </p>

        {error && (
          <div style={{
            backgroundColor: "#FEE2E2",
            color: "#DC2626",
            padding: "12px 16px",
            borderRadius: "0.75rem",
            marginBottom: "20px",
            border: "2px solid #FECACA",
            fontWeight: "600"
          }}>
            {error}
          </div>
        )}

        <div style={{
          display: "flex",
          flexDirection: "column",
          gap: "16px",
          marginBottom: "2rem"
        }}>
          {problemCategories.map((category) => (
            <label
              key={category}
              style={{
                display: "flex",
                alignItems: "center",
                padding: "16px 20px",
                border: selectedCategories.includes(category)
                  ? "2px solid #3B82F6"
                  : "2px solid #E5E7EB",
                borderRadius: "0.75rem",
                cursor: "pointer",
                backgroundColor: selectedCategories.includes(category)
                  ? "#EFF6FF"
                  : "#ffffff",
                transition: "all 0.2s ease-in-out",
                boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
              }}
              onMouseOver={(e) => {
                if (!selectedCategories.includes(category)) {
                  e.currentTarget.style.borderColor = "#BFDBFE";
                  e.currentTarget.style.backgroundColor = "#F8FAFC";
                }
              }}
              onMouseOut={(e) => {
                if (!selectedCategories.includes(category)) {
                  e.currentTarget.style.borderColor = "#E5E7EB";
                  e.currentTarget.style.backgroundColor = "#ffffff";
                }
              }}
            >
              <input
                type="checkbox"
                checked={selectedCategories.includes(category)}
                onChange={() => handleCategoryToggle(category)}
                style={{
                  marginRight: "14px",
                  width: "20px",
                  height: "20px",
                  cursor: "pointer",
                  accentColor: "#3B82F6"
                }}
              />
              <span style={{
                fontSize: "1rem",
                fontWeight: selectedCategories.includes(category) ? "600" : "500",
                color: selectedCategories.includes(category) ? "#1E40AF" : "#374151"
              }}>
                {category}
              </span>
            </label>
          ))}
        </div>

        <div style={{ textAlign: "center" }}>
          <button
            onClick={handleOkayClick}
            disabled={loading || selectedCategories.length === 0}
            style={{
              padding: "16px 32px",
              backgroundColor: loading || selectedCategories.length === 0 ? "#9CA3AF" : "#2563EB",
              color: "white",
              border: "none",
              borderRadius: "0.75rem",
              fontSize: "1.1rem",
              fontWeight: "600",
              cursor: loading || selectedCategories.length === 0 ? "not-allowed" : "pointer",
              transition: "all 0.2s ease-in-out",
              boxShadow: loading || selectedCategories.length === 0 ? "none" : "0 4px 6px rgba(0, 0, 0, 0.1)",
              transform: loading || selectedCategories.length === 0 ? "none" : "translateY(0)"
            }}
            onMouseOver={(e) => {
              if (!(loading || selectedCategories.length === 0)) {
                e.target.style.backgroundColor = "#1E40AF";
                e.target.style.transform = "translateY(-1px)";
                e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
              }
            }}
            onMouseOut={(e) => {
              if (!(loading || selectedCategories.length === 0)) {
                e.target.style.backgroundColor = "#2563EB";
                e.target.style.transform = "translateY(0)";
                e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
              }
            }}
          >
            {loading ? "Saving..." : "Okay"}
          </button>
        </div>

        <div style={{
          textAlign: "center",
          marginTop: "1.5rem",
          fontSize: "0.875rem",
          color: "#6B7280",
          fontWeight: "600"
        }}>
          Ticket #{ticketNumber}
        </div>
      </div>
    </div>
  );
}
